#!/bin/bash

echo "🚨 BodyMount BLE Connection Fix Script"
echo "======================================"

echo "📱 Step 1: Clearing Bluetooth cache..."
adb shell pm clear com.android.bluetooth
sleep 2

echo "🔄 Step 2: Restarting Bluetooth..."
adb shell settings put global bluetooth_on 0
sleep 3
adb shell settings put global bluetooth_on 1
sleep 3

echo "📋 Step 3: Checking paired devices..."
adb shell dumpsys bluetooth_manager | grep -A 10 "Bonded devices"

echo "🔧 Step 4: Installing enhanced APK..."
cd src && ./gradlew assembleDebug
adb install -r src/app/build/outputs/apk/debug/app-debug.apk

echo "🚀 Step 5: Starting app with monitoring..."
adb shell am force-stop com.bodymount.app
adb logcat -c
adb shell am start -n com.bodymount.app/.activities.WifiConfigActivity &

echo "📊 Step 6: Monitoring logs for connection..."
echo "Look for these success indicators:"
echo "  ✅ '=== STARTING CONNECTION SETUP ==='"
echo "  ✅ 'Device cache cleared'"
echo "  ✅ 'Successfully connected to device'"
echo "  ✅ '=== COMPREHENSIVE SERVICE DISCOVERY STARTED ==='"
echo ""
echo "If you see Status 142 errors, manually:"
echo "  1. Turn off BodyMount device"
echo "  2. Go to Settings > Bluetooth > Forget device"
echo "  3. Turn on BodyMount device in pairing mode"
echo "  4. Re-pair the device"
echo ""

adb logcat | grep -E "(BodyMountBleManager|GATT|Status 142|bluetooth)"
