# 🚨 STATUS 142 ERROR FIX GUIDE

## 🔍 **ROOT CAUSE IDENTIFIED**

Based on the logcat analysis (`Advantech-AIM-75-Android-12_2025-09-01_151922.logcat`), the issue is **NOT in our code** but a **device pairing/bonding problem**:

### **Critical Issues Found:**
1. **Status 142 Errors**: `GATT_CONN_TERMINATE_PEER_USER` - Device is terminating connections
2. **HID Interference**: `"ignore HID ind/notification"` - Bluetooth stack confusing device with HID
3. **Connection Loop**: Multiple connection IDs (3, 4, 5) all failing with status 142
4. **No App-Level Logs**: Our enhanced BluetoothManager is not being triggered

## 🛠️ **IMMEDIATE SOLUTION STEPS**

### **Step 1: Clear Bluetooth Cache (CRITICAL)**
```bash
# Method 1: ADB Commands
adb shell pm clear com.android.bluetooth
adb shell settings put global bluetooth_on 0
sleep 2
adb shell settings put global bluetooth_on 1

# Method 2: Manual (Recommended)
# Go to Settings > Apps > Bluetooth > Storage > Clear Cache & Clear Data
# Then restart Bluetooth
```

### **Step 2: Unpair and Re-pair Device**
```bash
# Check currently paired devices
adb shell dumpsys bluetooth_manager | grep -A 5 "Bonded devices"

# Force unpair (replace XX:XX:XX:XX:XX:XX with device MAC)
adb shell am start -a android.bluetooth.device.action.PAIRING_REQUEST \
  -e android.bluetooth.device.extra.DEVICE "XX:XX:XX:XX:XX:XX"
```

### **Step 3: Manual Device Reset**
1. **Turn off the BodyMount device completely**
2. **Hold power button for 10+ seconds** (factory reset)
3. **Turn device back on**
4. **Put device in pairing mode**

### **Step 4: Force BLE GATT Connection**
Our enhanced code now includes:
- **HID interference detection**
- **Forced LE transport mode**
- **Automatic device cache clearing**
- **Enhanced bonding retry mechanisms**

## 🔧 **ENHANCED CODE FEATURES DEPLOYED**

### **1. Connection Issue Detection**
```kotlin
private fun clearConnectionIssues(device: BluetoothDevice) {
    // Detect HID interference
    if (device.bluetoothClass?.majorDeviceClass == BluetoothClass.Device.Major.PERIPHERAL) {
        Log.w("BodyMountBleManager", "Device detected as HID peripheral - forcing GATT mode")
    }
    
    // Clear device cache
    val refreshMethod = device.javaClass.getMethod("refresh")
    refreshMethod.invoke(device)
}
```

### **2. Status 142 Specific Handling**
```kotlin
if (status == 142) {
    Log.w("BodyMountBleManager", "Status 142 detected - attempting device rebond")
    attemptDeviceRebond(dev)
}
```

### **3. Enhanced Error Analysis**
```kotlin
142 -> {
    Log.e("BodyMountBleManager", "GATT_CONN_TERMINATE_PEER_USER (142)")
    Log.e("BodyMountBleManager", "This often indicates HID interference or bonding issues")
    Log.e("BodyMountBleManager", "Recommended: Clear Bluetooth cache and re-pair device")
}
```

## 📱 **TESTING PROCEDURE**

### **1. Deploy Enhanced APK** ✅
```bash
cd src && ./gradlew assembleDebug
adb install -r src/app/build/outputs/apk/debug/app-debug.apk
```

### **2. Clear Bluetooth Cache**
```bash
# Clear system Bluetooth cache
adb shell pm clear com.android.bluetooth
adb reboot  # Recommended for complete reset
```

### **3. Monitor Enhanced Logs**
```bash
# Monitor our enhanced connection logs
adb logcat -c && adb logcat | grep -E "(BodyMountBleManager|GATT|bluetooth)"
```

### **4. Expected Success Logs**
```
D BodyMountBleManager: === STARTING CONNECTION SETUP ===
D BodyMountBleManager: Device: F0:9E:9E:0F:E9:25, Name: BMPMS
D BodyMountBleManager: Bond state: 12
D BodyMountBleManager: Clearing connection issues for F0:9E:9E:0F:E9:25
D BodyMountBleManager: Device cache cleared
D BodyMountBleManager: ✅ Successfully connected to device: F0:9E:9E:0F:E9:25
D BodyMountBleManager: === COMPREHENSIVE SERVICE DISCOVERY STARTED ===
```

## 🎯 **SUCCESS INDICATORS**

### **✅ Connection Success:**
- No more Status 142 errors
- Service discovery logs appear
- Multiple characteristics discovered
- Notification setup begins

### **✅ Data Streaming Success:**
```
D BodyMountBleManager: 💓 ECG_ONE notification: 0x01 0x02 (2 bytes)
D BodyMountBleManager: 🔴 PPG notification: 0x03 0x04 (2 bytes)
D BodyMountBleManager: 🫁 SPO2 notification: 0x05 0x06 (2 bytes)
```

## 🚨 **TROUBLESHOOTING**

### **If Status 142 Persists:**
1. **Device Hardware Issue**: Try connecting with another phone/tablet
2. **Device Firmware**: Contact manufacturer for firmware update
3. **Android Bluetooth Stack**: Try on different Android version

### **If HID Interference Continues:**
1. **Disable HID Services**: `adb shell pm disable com.android.bluetooth.hid`
2. **Force LE Transport**: Our code now forces BLE transport mode
3. **Use Different Bluetooth Adapter**: Try USB BLE dongle

### **If No App Logs Appear:**
1. **Check App Permissions**: Ensure all Bluetooth permissions granted
2. **Verify App Launch**: Use `adb shell am start -n com.bodymount.app/.MainActivity`
3. **Check Device Compatibility**: Verify Android version compatibility

## 📊 **MONITORING COMMANDS**

### **Real-time Connection Monitoring:**
```bash
# Monitor connection attempts
adb logcat -c && adb logcat | grep -E "(GATT|bluetooth|BodyMountBleManager)"

# Monitor app-specific logs
adb logcat -s BodyMountBleManager:D

# Monitor system Bluetooth
adb logcat -s BluetoothGatt:D
```

### **Device State Check:**
```bash
# Check Bluetooth status
adb shell dumpsys bluetooth_manager

# Check paired devices
adb shell settings get global bluetooth_on
```

## 🎉 **EXPECTED OUTCOME**

After following these steps:
1. **Status 142 errors eliminated**
2. **Stable BLE GATT connection established**
3. **Service discovery successful**
4. **Multiple characteristics streaming data**
5. **Waveform display in app UI**

The enhanced code provides comprehensive debugging and automatic recovery mechanisms. Once the device pairing issue is resolved, all waveform data should stream properly! 🚀
